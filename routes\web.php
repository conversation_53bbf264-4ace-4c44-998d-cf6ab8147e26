<?php

use App\Http\Controllers\AdminController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\DemoController;
use App\Http\Controllers\FollowerController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\NotificationPreferencesController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReactionController;
use App\Http\Controllers\ScholarshipController;
use App\Http\Controllers\ShareController;
use App\Http\Controllers\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function (Request $request) {
        $currentUser = auth()->user();

        // Get regular posts
        $postsQuery = \App\Models\Post::with([
                'user',
                'organization',
                'group',
                'postMethod',
                'tags',
                'comments' => function ($query) {
                    $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
                },
                'likes',
                'shares',
                'fileAttachments'
            ])
            ->published()
            ->visibleInFeed();

        // Filter by following if requested
        if ($request->has('following') && $request->following === 'true') {
            $followingUserIds = $currentUser->following()->pluck('users.id')->toArray();
            $followingUserIds[] = $currentUser->id; // Include own posts
            $postsQuery->whereIn('user_id', $followingUserIds);
        }

        // Get shared posts (timeline shares only) - filtered by privacy scope
        $sharesQuery = \App\Models\Share::with([
                'user',
                'likes',
                'comments' => function ($query) {
                    $query->with('user', 'replies.user')->whereNull('parent_id')->latest();
                },
                'post.user',
                'post.organization',
                'post.postMethod',
                'post.tags',
                'post.comments' => function ($query) {
                    $query->with('user', 'replies.user')->whereNull('parent_id')->latest();
                },
                'post.likes',
                'post.shares',
                'post.fileAttachments'
            ])
            ->where('share_type', 'direct')
            ->visibleTo(auth()->user())
            ->whereHas('post', function($query) {
                $query->published()->visibleInFeed();
            });

        // Filter shares by following if requested
        if ($request->has('following') && $request->following === 'true') {
            $followingUserIds = $currentUser->following()->pluck('users.id')->toArray();
            $followingUserIds[] = $currentUser->id; // Include own shares
            $sharesQuery->whereIn('user_id', $followingUserIds);
        }

        // Apply filters to both queries if provided
        if ($request->has('type') && $request->type !== 'all') {
            $postsQuery->where('type', $request->type);
            $sharesQuery->whereHas('post', function($query) use ($request) {
                $query->where('type', $request->type);
            });
        }

        if ($request->has('organization_filter')) {
            if ($request->organization_filter === 'personal') {
                $postsQuery->whereNull('organization_id');
                $sharesQuery->whereHas('post', function($query) {
                    $query->whereNull('organization_id');
                });
            } elseif ($request->organization_filter === 'organizations') {
                $postsQuery->whereNotNull('organization_id');
                $sharesQuery->whereHas('post', function($query) {
                    $query->whereNotNull('organization_id');
                });
            }
        }

        if ($request->has('with_images') && $request->with_images === 'true') {
            $postsQuery->whereNotNull('images')->where('images', '!=', '[]');
            $sharesQuery->whereHas('post', function($query) {
                $query->whereNotNull('images')->where('images', '!=', '[]');
            });
        }

        if ($request->has('pinned') && $request->pinned === 'true') {
            $postsQuery->where('is_pinned', true);
            $sharesQuery->whereHas('post', function($query) {
                $query->where('is_pinned', true);
            });
        }

        // Get posts and shares
        $posts = $postsQuery->latest('published_at')->get();
        $shares = $sharesQuery->latest('created_at')->get();

        // Combine and sort by date
        $feedItems = collect();

        // Add posts with type indicator
        foreach ($posts as $post) {
            $feedItems->push((object)[
                'type' => 'post',
                'data' => $post,
                'created_at' => $post->published_at,
            ]);
        }

        // Add shares with type indicator
        foreach ($shares as $share) {
            $feedItems->push((object)[
                'type' => 'share',
                'data' => $share,
                'created_at' => $share->created_at,
            ]);
        }

        // Sort by date (newest first) and paginate
        $feedItems = $feedItems->sortByDesc('created_at');

        // Manual pagination
        $perPage = 10;
        $currentPage = $request->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedItems = $feedItems->slice($offset, $perPage);

        $posts = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedItems,
            $feedItems->count(),
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        // Return JSON for AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'posts' => $posts->items(),
                'pagination' => [
                    'current_page' => $posts->currentPage(),
                    'last_page' => $posts->lastPage(),
                    'total' => $posts->total(),
                    'has_more' => $posts->hasMorePages()
                ]
            ]);
        }

        return view('dashboard', compact('posts'));
    })->name('dashboard');

    Route::get('/announcements', function () {
        return view('announcements.index');
    })->name('announcements');

    // Post routes
    Route::get('/posts', [PostController::class, 'index'])->name('posts.index');
    Route::get('/posts/create', [PostController::class, 'create'])->name('posts.create');
    Route::post('/posts', [PostController::class, 'store'])->name('posts.store');
    Route::get('/posts/{post}', [PostController::class, 'show'])->name('posts.show')->whereNumber('post');
    Route::get('/posts/{post}/edit', [PostController::class, 'edit'])->name('posts.edit')->whereNumber('post');
    Route::put('/posts/{post}', [PostController::class, 'update'])->name('posts.update')->whereNumber('post');
    Route::delete('/posts/{post}', [PostController::class, 'destroy'])->name('posts.destroy')->whereNumber('post');
    Route::post('/posts/{post}/like', [PostController::class, 'toggleLike'])->name('posts.like')->whereNumber('post');
    Route::get('/posts-filter', [PostController::class, 'filter'])->name('posts.filter');

    // Tags API route
    Route::get('/api/tags', [PostController::class, 'getAllTags'])->name('api.tags');

    // User search API route
    Route::get('/api/users/search', [UserController::class, 'search'])->name('api.users.search');

    // Organization member search API route
    Route::get('/api/organizations/{organization}/members/search', [OrganizationController::class, 'searchMembers'])->name('api.organizations.members.search');

    // Post method tags API route
    Route::get('/api/post-methods/tags', [PostController::class, 'getTagsByPostMethod'])->name('api.post-methods.tags');

    // Comment routes
    Route::get('/posts/{post}/comments', [CommentController::class, 'index'])->name('posts.comments.index')->whereNumber('post');
    Route::post('/posts/{post}/comments', [CommentController::class, 'store'])->name('posts.comments.store')->whereNumber('post');
    Route::put('/comments/{comment}', [CommentController::class, 'update'])->name('comments.update');
    Route::delete('/comments/{comment}', [CommentController::class, 'destroy'])->name('comments.destroy');
    Route::post('/comments/{comment}/like', [CommentController::class, 'toggleLike'])->name('comments.like');

    // Share routes
    Route::post('/posts/{post}/share/timeline', [ShareController::class, 'shareToTimeline'])->name('posts.share.timeline')->whereNumber('post');
    Route::post('/posts/{post}/share/external', [ShareController::class, 'shareExternal'])->name('posts.share.external')->whereNumber('post');
    Route::get('/posts/{post}/shares', [ShareController::class, 'getShares'])->name('posts.shares')->whereNumber('post');
    Route::put('/shares/{share}', [ShareController::class, 'update'])->name('shares.update');
    Route::delete('/shares/{share}', [ShareController::class, 'destroy'])->name('shares.destroy');
    Route::post('/shares/{share}/like', [ShareController::class, 'toggleLike'])->name('shares.like');

    // Share comment routes
    Route::post('/shares/{share}/comments', [\App\Http\Controllers\ShareCommentController::class, 'store'])->name('shares.comments.store');
    Route::put('/share-comments/{comment}', [\App\Http\Controllers\ShareCommentController::class, 'update'])->name('share-comments.update');
    Route::delete('/share-comments/{comment}', [\App\Http\Controllers\ShareCommentController::class, 'destroy'])->name('share-comments.destroy');
    Route::post('/share-comments/{comment}/like', [\App\Http\Controllers\ShareCommentController::class, 'toggleLike'])->name('share-comments.like');

    // Reaction routes
    Route::post('/reactions', [\App\Http\Controllers\ReactionController::class, 'store'])->name('reactions.store');
    Route::get('/reactions/counts', [\App\Http\Controllers\ReactionController::class, 'getCounts'])->name('reactions.counts');
    Route::get('/posts/{post}/summary', [\App\Http\Controllers\ReactionController::class, 'getPostSummary'])->name('posts.summary');

    // Organization Request routes (authenticated)
    Route::get('/organization-requests', [\App\Http\Controllers\OrganizationRequestController::class, 'index'])->name('organization-requests.index');
    Route::get('/organization-requests/create', [\App\Http\Controllers\OrganizationRequestController::class, 'create'])->name('organization-requests.create');
    Route::post('/organization-requests', [\App\Http\Controllers\OrganizationRequestController::class, 'store'])->name('organization-requests.store');
    Route::get('/organization-requests/{organizationRequest}', [\App\Http\Controllers\OrganizationRequestController::class, 'show'])->name('organization-requests.show');

    // Organization routes (authenticated)
    Route::get('/organizations/my', [OrganizationController::class, 'my'])->name('organizations.my');
    Route::get('/organizations/create', [OrganizationController::class, 'create'])->name('organizations.create');
    Route::post('/organizations', [OrganizationController::class, 'store'])->name('organizations.store');
    Route::get('/organizations/{organization}/edit', [OrganizationController::class, 'edit'])->name('organizations.edit');
    Route::put('/organizations/{organization}', [OrganizationController::class, 'update'])->name('organizations.update');
    Route::delete('/organizations/{organization}', [OrganizationController::class, 'destroy'])->name('organizations.destroy');
    Route::post('/organizations/{organization}/join', [OrganizationController::class, 'join'])->name('organizations.join');
    Route::delete('/organizations/{organization}/leave', [OrganizationController::class, 'leave'])->name('organizations.leave');

    // Officer Management
    Route::get('/organizations/{organization}/officers', [OrganizationController::class, 'officers'])->name('organizations.officers');
    Route::post('/organizations/{organization}/officers', [OrganizationController::class, 'addOfficer'])->name('organizations.add-officer');
    Route::delete('/organizations/{organization}/officers', [OrganizationController::class, 'removeOfficer'])->name('organizations.remove-officer');

    // Organization Dashboard
    Route::get('/organizations/{organization}/dashboard', [OrganizationController::class, 'dashboard'])->name('organizations.dashboard');

    // Organization Page Mode routes (authenticated)
    Route::post('/organizations/{organization}/follow', [OrganizationController::class, 'follow'])->name('organizations.follow');
    Route::delete('/organizations/{organization}/unfollow', [OrganizationController::class, 'unfollow'])->name('organizations.unfollow');
    Route::post('/organizations/{organization}/toggle-page-mode', [OrganizationController::class, 'togglePageMode'])->name('organizations.toggle-page-mode');
    Route::get('/organizations/{organization}/followers', [OrganizationController::class, 'followers'])->name('organizations.followers');

    // Group routes (authenticated only)
    Route::get('/groups/my', [GroupController::class, 'my'])->name('groups.my');
    Route::get('/groups/create', [GroupController::class, 'create'])->name('groups.create');
    Route::post('/groups', [GroupController::class, 'store'])->name('groups.store');
    Route::get('/groups/{group}/edit', [GroupController::class, 'edit'])->name('groups.edit');
    Route::put('/groups/{group}', [GroupController::class, 'update'])->name('groups.update');
    Route::delete('/groups/{group}', [GroupController::class, 'destroy'])->name('groups.destroy');
    Route::post('/groups/{group}/join', [GroupController::class, 'join'])->name('groups.join');
    Route::delete('/groups/{group}/leave', [GroupController::class, 'leave'])->name('groups.leave');

    // Group member management routes
    Route::get('/groups/{group}/members', [GroupController::class, 'members'])->name('groups.members');
    Route::post('/groups/{group}/members/{user}/approve', [GroupController::class, 'approveMember'])->name('groups.approve-member');
    Route::delete('/groups/{group}/members/{user}/reject', [GroupController::class, 'rejectMember'])->name('groups.reject-member');
    Route::delete('/groups/{group}/members/{user}/remove', [GroupController::class, 'removeMember'])->name('groups.remove-member');
    Route::patch('/groups/{group}/members/{user}/role', [GroupController::class, 'updateMemberRole'])->name('groups.update-member-role');

    // Group post moderation routes
    Route::get('/groups/{group}/pending-posts', [GroupController::class, 'pendingPosts'])->name('groups.pending-posts');
    Route::post('/groups/{group}/posts/{post}/approve', [GroupController::class, 'approvePost'])->name('groups.approve-post');
    Route::post('/groups/{group}/posts/{post}/reject', [GroupController::class, 'rejectPost'])->name('groups.reject-post');

    // Scholarship routes
    Route::get('/scholarships', [ScholarshipController::class, 'index'])->name('scholarships.index');
    Route::get('/scholarships/create', [ScholarshipController::class, 'create'])->name('scholarships.create')->middleware('role:admin,org_officer');
    Route::post('/scholarships', [ScholarshipController::class, 'store'])->name('scholarships.store')->middleware('role:admin,org_officer');
    Route::get('/scholarships/{scholarship}', [ScholarshipController::class, 'show'])->name('scholarships.show');
    Route::get('/scholarships/{scholarship}/edit', [ScholarshipController::class, 'edit'])->name('scholarships.edit');
    Route::put('/scholarships/{scholarship}', [ScholarshipController::class, 'update'])->name('scholarships.update');
    Route::delete('/scholarships/{scholarship}', [ScholarshipController::class, 'destroy'])->name('scholarships.destroy');

    // Admin routes - protected by role middleware
    Route::prefix('admin')->name('admin.')->middleware('role:admin,org_officer')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/users', [AdminController::class, 'users'])->name('users.index');
        Route::get('/posts', [AdminController::class, 'posts'])->name('posts.index');
        Route::get('/organizations', [AdminController::class, 'organizations'])->name('organizations.index');
        Route::get('/groups', [AdminController::class, 'groups'])->name('groups.index');
        Route::get('/scholarships', [AdminController::class, 'scholarships'])->name('scholarships.index');
        Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics');

        // Organization Request Management
        Route::get('/organization-requests', [AdminController::class, 'organizationRequests'])->name('organization-requests.index');
        Route::get('/organization-requests/{organizationRequest}', [AdminController::class, 'showOrganizationRequest'])->name('organization-requests.show');
        Route::post('/organization-requests/{organizationRequest}/approve', [AdminController::class, 'approveOrganizationRequest'])->name('organization-requests.approve');
        Route::post('/organization-requests/{organizationRequest}/reject', [AdminController::class, 'rejectOrganizationRequest'])->name('organization-requests.reject');
    });
});

// Public organization and group routes
Route::get('/organizations', [OrganizationController::class, 'index'])->name('organizations.index');
Route::get('/organizations/{organization}', [OrganizationController::class, 'show'])->name('organizations.show');
Route::get('/pages/{organization}', [OrganizationController::class, 'page'])->name('pages.show');
Route::get('/groups', [GroupController::class, 'index'])->name('groups.index');
Route::get('/groups/{group}', [GroupController::class, 'show'])->name('groups.show');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/{user}', [ProfileController::class, 'show'])->name('profile.user');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile/background-photo', [ProfileController::class, 'deleteBackgroundPhoto'])->name('profile.background-photo.delete');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User follower routes
    Route::post('/users/{user}/follow', [FollowerController::class, 'follow'])->name('users.follow');
    Route::delete('/users/{user}/unfollow', [FollowerController::class, 'unfollow'])->name('users.unfollow');
    Route::post('/users/{user}/toggle-follow', [FollowerController::class, 'toggleFollow'])->name('users.toggle-follow');
    Route::get('/users/{user}/followers', [FollowerController::class, 'followers'])->name('users.followers');
    Route::get('/users/{user}/following', [FollowerController::class, 'following'])->name('users.following');

    // Follow management routes
    Route::get('/my/followers', [App\Http\Controllers\FollowManagementController::class, 'followers'])->name('follow-management.followers');
    Route::get('/my/following', [App\Http\Controllers\FollowManagementController::class, 'following'])->name('follow-management.following');

    // Notification routes
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('notifications.read-all');
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy'])->name('notifications.destroy');
    Route::get('/notifications/unread-count', [NotificationController::class, 'unreadCount'])->name('notifications.unread-count');

    // Notification preferences routes
    Route::get('/notification-preferences', [NotificationPreferencesController::class, 'show'])->name('notification-preferences.show');
    Route::patch('/notification-preferences', [NotificationPreferencesController::class, 'update'])->name('notification-preferences.update');
});

require __DIR__.'/auth.php';
