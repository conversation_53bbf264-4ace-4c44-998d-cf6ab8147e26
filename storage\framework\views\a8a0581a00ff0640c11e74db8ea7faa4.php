<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Success Messages -->
    <?php if(session('status') === 'profile-updated'): ?>
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Profile updated successfully!
            </div>
        </div>
    <?php endif; ?>

    <?php if(session('status') === 'password-updated'): ?>
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Password updated successfully!
            </div>
        </div>
    <?php endif; ?>

    <?php if(session('status') === 'background-photo-deleted'): ?>
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Background photo removed successfully!
            </div>
        </div>
    <?php endif; ?>

    <!-- Profile Header Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Photo -->
        <div class="h-48 sm:h-64 profile-cover relative"
             <?php if($user->background_photo): ?>
                style="background-image: url('<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($user->background_photo)); ?>'); background-size: cover; background-position: center;"
             <?php endif; ?>>
            <div class="absolute inset-0 bg-gradient-to-br from-black/10 to-black/30"></div>
            <!-- Cover photo management buttons (if own profile) -->
            <?php if(auth()->id() === $user->id): ?>
                <div class="absolute bottom-4 right-4 flex space-x-2">
                    <!-- Upload/Edit Background Photo Button -->
                    <button onclick="document.getElementById('backgroundPhotoInput').click()"
                            class="bg-green-500/90 hover:bg-custom-green text-white px-3 py-2 rounded-lg text-sm font-medium transition-all shadow-sm hover:shadow-md">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        </svg>
                        <span class="hidden sm:inline"><?php echo e($user->background_photo ? 'Edit Cover' : 'Add Cover'); ?></span>
                    </button>

                    <!-- Delete Background Photo Button (only show if photo exists) -->
                    <?php if($user->background_photo): ?>
                        <button onclick="deleteBackgroundPhoto()"
                                class="bg-red-500/90 hover:bg-red-500 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all shadow-sm hover:shadow-md">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            <span class="hidden sm:inline">Remove</span>
                        </button>
                    <?php endif; ?>
                </div>

                <!-- Hidden file input for background photo -->
                <form id="backgroundPhotoForm" action="<?php echo e(route('profile.update')); ?>" method="POST" enctype="multipart/form-data" style="display: none;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PATCH'); ?>
                    <input type="file" id="backgroundPhotoInput" name="background_photo" accept="image/*" onchange="uploadBackgroundPhoto()">
                </form>
            <?php endif; ?>
        </div>

        <!-- Profile Info -->
        <div class="px-4 sm:px-6 pb-6">
            <div class="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                <!-- Profile Picture -->
                <div class="relative -mt-12 sm:-mt-16 mb-4 sm:mb-0 flex-shrink-0">
                    <img class="w-24 h-24 sm:w-32 sm:h-32 rounded-full profile-avatar-border"
                         src="<?php echo e($user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE&size=128'); ?>"
                         alt="<?php echo e($user->name); ?>">
                    <?php if(auth()->id() === $user->id): ?>
                        <button class="absolute bottom-1 right-1 sm:bottom-2 sm:right-2 bg-white hover:bg-gray-50 rounded-full p-1.5 sm:p-2 shadow-lg transition-all hover:shadow-xl border border-gray-200">
                            <svg class="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            </svg>
                        </button>
                    <?php endif; ?>
                </div>

                <!-- User Info -->
                <div class="flex-1 min-w-0">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div class="flex-1 min-w-0">
                            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 truncate"><?php echo e($user->name); ?></h1>
                            <?php if($user->bio): ?>
                                <p class="text-gray-600 mt-1 text-sm sm:text-base line-clamp-2"><?php echo e($user->bio); ?></p>
                            <?php endif; ?>
                            <div class="flex flex-wrap items-center gap-x-4 gap-y-1 mt-2 text-xs sm:text-sm text-gray-500">
                                <?php if($user->student_id): ?>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                        </svg>
                                        ID: <?php echo e($user->student_id); ?>

                                    </span>
                                <?php endif; ?>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    <?php echo e(ucfirst($user->role)); ?>

                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L14 7" />
                                    </svg>
                                    Joined <?php echo e($user->created_at->format('M Y')); ?>

                                </span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2 mt-4 sm:mt-0 sm:ml-4">
                            <?php if(auth()->id() !== $user->id): ?>
                                <button class="bg-custom-green text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-all font-medium text-sm shadow-sm hover:shadow-md">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                    Message
                                </button>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user-follower', ['user' => $user]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2169708765-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php else: ?>
                                <a href="<?php echo e(route('profile.edit')); ?>" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all font-medium text-sm shadow-sm hover:shadow-md">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    Edit Profile
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-3 sm:grid-cols-7 gap-4 mt-6 pt-6 border-t border-gray-200">
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($stats['posts_count']); ?></div>
                    <div class="text-xs sm:text-sm text-gray-500">Posts</div>
                </div>
                <?php if(auth()->id() === $user->id): ?>
                    <a href="<?php echo e(route('follow-management.followers')); ?>" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($user->followers()->count()); ?></div>
                        <div class="text-xs sm:text-sm text-gray-500">Followers</div>
                    </a>
                    <a href="<?php echo e(route('follow-management.following')); ?>" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($user->following()->count()); ?></div>
                        <div class="text-xs sm:text-sm text-gray-500">Following</div>
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(route('users.followers', $user)); ?>" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($user->followers()->count()); ?></div>
                        <div class="text-xs sm:text-sm text-gray-500">Followers</div>
                    </a>
                    <a href="<?php echo e(route('users.following', $user)); ?>" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($user->following()->count()); ?></div>
                        <div class="text-xs sm:text-sm text-gray-500">Following</div>
                    </a>
                <?php endif; ?>
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($stats['organizations_count']); ?></div>
                    <div class="text-xs sm:text-sm text-gray-500">Organizations</div>
                </div>
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($stats['created_organizations_count']); ?></div>
                    <div class="text-xs sm:text-sm text-gray-500">Created</div>
                </div>
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($stats['total_likes']); ?></div>
                    <div class="text-xs sm:text-sm text-gray-500">Likes</div>
                </div>
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($stats['total_comments']); ?></div>
                    <div class="text-xs sm:text-sm text-gray-500">Comments</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="max-w-4xl mx-auto" x-data="{
        activeTab: 'posts',
        expandedSections: {
            academic: true,
            personal: false,
            skills: true,
            social: false,
            achievements: true
        },
        toggleSection(section) {
            this.expandedSections[section] = !this.expandedSections[section];
        }
    }">
        <!-- Tab Navigation -->
        <div class="bg-white rounded-xl shadow-md border border-gray-200 mb-6 sticky top-0 z-10">
            <div class="px-6 py-4">
                <nav class="flex space-x-8" aria-label="Profile Tabs">
                    <button @click="activeTab = 'posts'"
                            :class="activeTab === 'posts' ? 'border-custom-green text-custom-green bg-custom-green/5' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-2 px-4 border-b-2 font-medium text-sm transition-all rounded-t-lg flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Recent Posts
                    </button>
                    <button @click="activeTab = 'profile'"
                            :class="activeTab === 'profile' ? 'border-custom-green text-custom-green bg-custom-green/5' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-2 px-4 border-b-2 font-medium text-sm transition-all rounded-t-lg flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Profile Info
                    </button>
                    <?php if(auth()->id() === $user->id): ?>
                        <button @click="activeTab = 'actions'"
                                :class="activeTab === 'actions' ? 'border-custom-green text-custom-green bg-custom-green/5' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="whitespace-nowrap py-2 px-4 border-b-2 font-medium text-sm transition-all rounded-t-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Quick Actions
                        </button>
                    <?php endif; ?>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="space-y-6">
            <!-- Profile Info Tab -->
            <div x-show="activeTab === 'profile'" x-transition class="space-y-6">
                <!-- 📘 Academic Information Section -->
            <?php
                $profileVisibility = $user->getProfileVisibility();
                $showAcademicInfo = $profileVisibility['academic_info'] || auth()->id() === $user->id;
                $hasAcademicInfo = $user->student_id || $user->campus || $user->course_program || $user->year_level || $user->enrollment_status || $user->college_department;
            ?>
            <?php if($showAcademicInfo && $hasAcademicInfo): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    <div class="p-6 border-b border-gray-100">
                        <button @click="toggleSection('academic')" class="w-full flex items-center justify-between text-left">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">📘 Academic Information</h2>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 transition-transform" :class="expandedSections.academic ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div x-show="expandedSections.academic" x-transition class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php if($user->student_id): ?>
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Student ID</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->student_id); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($user->campus): ?>
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Campus</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->campus); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($user->course_program): ?>
                                <div class="flex items-start space-x-3 md:col-span-2">
                                    <div class="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Course / Program</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->course_program); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($user->year_level): ?>
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Year Level</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->year_level); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($user->enrollment_status): ?>
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Enrollment Status</p>
                                        <p class="text-sm font-medium text-gray-900">
                                            <?php if($user->enrollment_status === 'active'): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                            <?php elseif($user->enrollment_status === 'loa'): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Leave of Absence</span>
                                            <?php elseif($user->enrollment_status === 'graduated'): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Graduated</span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($user->college_department): ?>
                                <div class="flex items-start space-x-3 md:col-span-2">
                                    <div class="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">College / Department</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->college_department); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>



            <!-- 🧍‍♂️ Personal Information Section -->
            <?php
                $showPersonalInfo = $profileVisibility['personal_info'] || auth()->id() === $user->id;
                $hasPersonalInfo = $user->contact_number || $user->phone || $user->address || $user->birthdate;
            ?>
            <?php if($showPersonalInfo && $hasPersonalInfo): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    <div class="p-6 border-b border-gray-100">
                        <button @click="toggleSection('personal')" class="w-full flex items-center justify-between text-left">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">🧍‍♂️ Personal Information</h2>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 transition-transform" :class="expandedSections.personal ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div x-show="expandedSections.personal" x-transition class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php if($user->contact_number || $user->phone): ?>
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0 w-8 h-8 bg-purple-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Contact Number</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->contact_number ?: $user->phone); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($user->birthdate): ?>
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0 w-8 h-8 bg-purple-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L14 7" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Birthdate</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->birthdate->format('F j, Y')); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($user->address): ?>
                                <div class="flex items-start space-x-3 md:col-span-2">
                                    <div class="flex-shrink-0 w-8 h-8 bg-purple-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-500">Address</p>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->address); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 🧠 Skills & Interests Section -->
            <?php
                $showSkillsInterests = $profileVisibility['skills_interests'] || auth()->id() === $user->id;
                $hasSkillsInterests = $user->skills_interests && count($user->skills_interests) > 0;
            ?>
            <?php if($showSkillsInterests && $hasSkillsInterests): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    <div class="p-6 border-b border-gray-100">
                        <button @click="toggleSection('skills')" class="w-full flex items-center justify-between text-left">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">🧠 Skills & Interests</h2>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 transition-transform" :class="expandedSections.skills ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div x-show="expandedSections.skills" x-transition class="p-6">
                        <div class="flex flex-wrap gap-2">
                            <?php $__currentLoopData = $user->skills_interests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-custom-green/10 text-custom-green border border-custom-green/20">
                                    <?php echo e($skill); ?>

                                </span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <!-- 🌐 Social Links Section -->
            <?php
                $showSocialLinks = $profileVisibility['social_links'] || auth()->id() === $user->id;
                $hasSocialLinks = $user->social_links && count($user->social_links) > 0;
            ?>
            <?php if($showSocialLinks && $hasSocialLinks): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    <div class="p-6 border-b border-gray-100">
                        <button @click="toggleSection('social')" class="w-full flex items-center justify-between text-left">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                                    </svg>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">🌐 Social Links</h2>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 transition-transform" :class="expandedSections.social ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div x-show="expandedSections.social" x-transition class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <?php $__currentLoopData = $user->social_links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($link['platform'] && $link['url']): ?>
                                    <a href="<?php echo e($link['url']); ?>" target="_blank" rel="noopener noreferrer" class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-custom-green hover:bg-custom-green/5 transition-all group">
                                        <div class="flex-shrink-0 w-8 h-8 bg-gray-100 group-hover:bg-custom-green/10 rounded-lg flex items-center justify-center">
                                            <?php switch($link['platform']):
                                                case ('LinkedIn'): ?>
                                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                                    </svg>
                                                    <?php break; ?>
                                                <?php case ('Facebook'): ?>
                                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                    </svg>
                                                    <?php break; ?>
                                                <?php case ('Twitter'): ?>
                                                    <svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                                    </svg>
                                                    <?php break; ?>
                                                <?php case ('Instagram'): ?>
                                                    <svg class="w-4 h-4 text-pink-600" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.02.43a5.105 5.105 0 00-1.852 1.207 5.105 5.105 0 00-1.207 1.852C1.734 4.02 1.612 4.594 1.578 5.541 1.544 6.489 1.53 6.896 1.53 10.517s.013 4.028.048 4.976c.034.947.156 1.521.382 2.021a5.105 5.105 0 001.207 1.852 5.105 5.105 0 001.852 1.207c.5.226 1.074.348 2.021.382.948.035 1.355.048 4.976.048s4.028-.013 4.976-.048c.947-.034 1.521-.156 2.021-.382a5.105 5.105 0 001.852-1.207 5.105 5.105 0 001.207-1.852c.226-.5.348-1.074.382-2.021.035-.948.048-1.355.048-4.976s-.013-4.028-.048-4.976c-.034-.947-.156-1.521-.382-2.021a5.105 5.105 0 00-1.207-1.852A5.105 5.105 0 0018.982.43C18.482.204 17.908.082 16.961.048 16.013.013 15.606 0 11.985 0h.032zm-.7 1.378c.348-.001.735-.001 1.117 0 3.558 0 3.97.013 4.897.048.835.038 1.29.177 1.592.294.4.155.686.341.986.641.3.3.486.586.641.986.117.302.256.757.294 1.592.035.927.048 1.339.048 4.897s-.013 3.97-.048 4.897c-.038.835-.177 1.29-.294 1.592a2.678 2.678 0 01-.641.986 2.678 2.678 0 01-.986.641c-.302.117-.757.256-1.592.294-.927.035-1.339.048-4.897.048s-3.97-.013-4.897-.048c-.835-.038-1.29-.177-1.592-.294a2.678 2.678 0 01-.986-.641 2.678 2.678 0 01-.641-.986c-.117-.302-.256-.757-.294-1.592-.035-.927-.048-1.339-.048-4.897s.013-3.97.048-4.897c.038-.835.177-1.29.294-1.592.155-.4.341-.686.641-.986a2.678 2.678 0 01.986-.641c.302-.117.757-.256 1.592-.294.81-.037 1.124-.048 3.78-.048l1.117.001zm.156 2.292a3.252 3.252 0 100 6.504 3.252 3.252 0 000-6.504zm0 1.378a1.874 1.874 0 110 3.748 1.874 1.874 0 010-3.748zm3.654-2.292a.76.76 0 11-1.52 0 .76.76 0 011.52 0z"/>
                                                    </svg>
                                                    <?php break; ?>
                                                <?php case ('GitHub'): ?>
                                                    <svg class="w-4 h-4 text-gray-900" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                                    </svg>
                                                    <?php break; ?>
                                                <?php case ('Portfolio'): ?>
                                                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                                                    </svg>
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                                    </svg>
                                            <?php endswitch; ?>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 group-hover:text-custom-green"><?php echo e($link['platform']); ?></p>
                                            <p class="text-xs text-gray-500 truncate"><?php echo e(parse_url($link['url'], PHP_URL_HOST)); ?></p>
                                        </div>
                                        <svg class="w-4 h-4 text-gray-400 group-hover:text-custom-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 🏆 Achievements Section -->
            <?php
                $showAchievements = $profileVisibility['achievements'] || auth()->id() === $user->id;
                $hasAchievements = $user->achievements && count($user->achievements) > 0;
            ?>
            <?php if($showAchievements && $hasAchievements): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    <div class="p-6 border-b border-gray-100">
                        <button @click="toggleSection('achievements')" class="w-full flex items-center justify-between text-left">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">🏆 Achievements</h2>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 transition-transform" :class="expandedSections.achievements ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div x-show="expandedSections.achievements" x-transition class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <?php $__currentLoopData = $user->achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                                    <div class="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-yellow-800"><?php echo e($achievement); ?></p>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Platform Achievements Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Platform Achievements</h3>
                    </div>
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs font-medium rounded-full"><?php echo e($stats['posts_count'] + $stats['organizations_count']); ?></span>
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <?php if($stats['posts_count'] >= 10): ?>
                        <div class="flex items-center space-x-2 p-2 bg-green-50 rounded-lg">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-green-800">Active Poster</p>
                                <p class="text-xs text-green-600">10+ posts</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($stats['organizations_count'] >= 3): ?>
                        <div class="flex items-center space-x-2 p-2 bg-blue-50 rounded-lg">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-blue-800">Community Leader</p>
                                <p class="text-xs text-blue-600">3+ organizations</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($stats['total_likes'] >= 50): ?>
                        <div class="flex items-center space-x-2 p-2 bg-red-50 rounded-lg">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-red-800">Well Liked</p>
                                <p class="text-xs text-red-600">50+ likes</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($user->created_at->diffInMonths(now()) >= 12): ?>
                        <div class="flex items-center space-x-2 p-2 bg-purple-50 rounded-lg">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-purple-800">Veteran</p>
                                <p class="text-xs text-purple-600">1+ year member</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if($stats['posts_count'] < 10 && $stats['organizations_count'] < 3 && $stats['total_likes'] < 50 && $user->created_at->diffInMonths(now()) < 12): ?>
                    <div class="text-center py-4">
                        <p class="text-sm text-gray-500">Keep engaging to unlock achievements!</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Organizations Section -->
            <?php if($user->activeOrganizations->count() > 0): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">Organizations</h2>
                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full"><?php echo e($user->activeOrganizations->count()); ?></span>
                    </div>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $user->activeOrganizations->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                                <img class="w-10 h-10 rounded-lg object-cover"
                                     src="<?php echo e($organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($organization->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                                     alt="<?php echo e($organization->name); ?>">
                                <div class="flex-1 min-w-0">
                                    <a href="<?php echo e(route('organizations.show', $organization)); ?>" class="font-medium text-gray-900 hover:text-custom-green truncate block text-sm">
                                        <?php echo e($organization->name); ?>

                                    </a>
                                    <p class="text-xs text-gray-500"><?php echo e(ucfirst($organization->pivot->role)); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($user->activeOrganizations->count() > 5): ?>
                            <div class="pt-2 border-t border-gray-100">
                                <a href="<?php echo e(route('organizations.my')); ?>" class="text-custom-green hover:text-custom-dark-gray text-sm font-medium flex items-center">
                                    View all organizations
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Activity Summary -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Activity Summary</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Posts Created</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900"><?php echo e($stats['posts_count']); ?></span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Content Shared</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900"><?php echo e($stats['shares_count']); ?></span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Likes Received</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900"><?php echo e($stats['total_likes']); ?></span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Comments Made</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900"><?php echo e($stats['total_comments']); ?></span>
                    </div>
                </div>
            </div>
            </div>

            <!-- Recent Posts Tab -->
            <div x-show="activeTab === 'posts'" x-transition class="space-y-6">
        <div class="space-y-6">
            <!-- Create Post (if own profile) -->
            <?php if(auth()->id() === $user->id): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                    <div class="flex items-center space-x-3">
                        <img class="h-10 w-10 rounded-full object-cover" src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                        <div class="flex-1">
                            <button onclick="openCreatePostModal()" class="w-full text-left px-4 py-3 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-600 transition-all placeholder-gray-500">
                                What's on your mind, <?php echo e(auth()->user()->name); ?>?
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                        <div class="flex space-x-4">
                            <button onclick="openCreatePostModal()" class="flex items-center space-x-2 text-gray-600 hover:text-custom-green transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span class="text-sm font-medium">Photo</span>
                            </button>
                            <button onclick="openCreatePostModal()" class="flex items-center space-x-2 text-gray-600 hover:text-custom-green transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 00-2.828-2.828z" />
                                </svg>
                                <span class="text-sm font-medium">File</span>
                            </button>
                        </div>
                        <button onclick="openCreatePostModal()" class="bg-custom-green text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-all text-sm font-medium">
                            Post
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Activity Filter Tabs -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden" x-data="{ activeFilter: 'all' }">
                <div class="border-b border-gray-200 bg-gray-50">
                    <nav class="flex space-x-8 px-6 py-3" aria-label="Tabs">
                        <button @click="activeFilter = 'all'" :class="activeFilter === 'all' ? 'border-custom-green text-custom-green bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm transition-all rounded-t-lg">
                            All Activity
                        </button>
                        <button @click="activeFilter = 'posts'" :class="activeFilter === 'posts' ? 'border-custom-green text-custom-green bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm transition-all rounded-t-lg">
                            Posts (<?php echo e($stats['posts_count']); ?>)
                        </button>
                        <button @click="activeFilter = 'shares'" :class="activeFilter === 'shares' ? 'border-custom-green text-custom-green bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm transition-all rounded-t-lg">
                            Shares (<?php echo e($stats['shares_count']); ?>)
                        </button>
                    </nav>
                </div>

                <!-- Activity Header -->
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-900">
                            <?php echo e(auth()->id() === $user->id ? 'Your Recent Activity' : $user->name . "'s Recent Activity"); ?>

                        </h2>
                        <div class="text-sm text-gray-500">
                            <span class="font-medium"><?php echo e($stats['total_activity_count']); ?></span> total activities
                        </div>
                    </div>
                </div>

                <!-- Activity Content -->
                <div class="divide-y divide-gray-100">
                    <?php $__empty_1 = true; $__currentLoopData = $recentActivity; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="p-6"
                             x-show="activeFilter === 'all' || (activeFilter === 'posts' && '<?php echo e($activity->type); ?>' === 'post') || (activeFilter === 'shares' && '<?php echo e($activity->type); ?>' === 'share')"
                             x-transition>
                            <?php if($activity->type === 'post'): ?>
                                <?php if (isset($component)) { $__componentOriginal14b498b52c33a1421ff8895e4557790f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal14b498b52c33a1421ff8895e4557790f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.post-card','data' => ['post' => $activity->data]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('post-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($activity->data)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $attributes = $__attributesOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__attributesOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $component = $__componentOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__componentOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
                            <?php elseif($activity->type === 'share'): ?>
                                <?php if (isset($component)) { $__componentOriginald4faf1cbcdbcb738455ac47166667811 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald4faf1cbcdbcb738455ac47166667811 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.shared-post-card','data' => ['share' => $activity->data]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shared-post-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($activity->data)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald4faf1cbcdbcb738455ac47166667811)): ?>
<?php $attributes = $__attributesOriginald4faf1cbcdbcb738455ac47166667811; ?>
<?php unset($__attributesOriginald4faf1cbcdbcb738455ac47166667811); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald4faf1cbcdbcb738455ac47166667811)): ?>
<?php $component = $__componentOriginald4faf1cbcdbcb738455ac47166667811; ?>
<?php unset($__componentOriginald4faf1cbcdbcb738455ac47166667811); ?>
<?php endif; ?>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="p-12 text-center">
                            <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No activity yet</h3>
                            <p class="text-gray-500 mb-4">
                                <?php echo e(auth()->id() === $user->id ? "You haven't created any posts or shared anything yet." : $user->name . " hasn't shared any posts or content yet."); ?>

                            </p>
                            <?php if(auth()->id() === $user->id): ?>
                                <button onclick="openCreatePostModal()" class="bg-custom-green text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-all font-medium">
                                    Create Your First Post
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            </div>

            <!-- Quick Actions Tab -->
            <?php if(auth()->id() === $user->id): ?>
                <div x-show="activeTab === 'actions'" x-transition class="space-y-6">
                    <!-- Profile Management Actions -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Profile Management</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <a href="<?php echo e(route('profile.edit')); ?>" class="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                                <div class="w-10 h-10 bg-green-100 group-hover:bg-green-200 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Edit Profile</p>
                                    <p class="text-sm text-gray-500">Update your information</p>
                                </div>
                            </a>

                            <button onclick="document.getElementById('avatarInput').click()" class="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group text-left">
                                <div class="w-10 h-10 bg-purple-100 group-hover:bg-purple-200 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Change Avatar</p>
                                    <p class="text-sm text-gray-500">Upload new profile picture</p>
                                </div>
                            </button>

                            <button onclick="document.getElementById('backgroundPhotoInput').click()" class="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group text-left">
                                <div class="w-10 h-10 bg-cyan-100 group-hover:bg-cyan-200 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Change Background</p>
                                    <p class="text-sm text-gray-500">Upload cover photo</p>
                                </div>
                            </button>

                            <button onclick="openCreatePostModal()" class="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group text-left">
                                <div class="w-10 h-10 bg-yellow-100 group-hover:bg-yellow-200 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Create Post</p>
                                    <p class="text-sm text-gray-500">Share something new</p>
                                </div>
                            </button>

                            <a href="<?php echo e(route('organizations.my')); ?>" class="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                                <div class="w-10 h-10 bg-blue-100 group-hover:bg-blue-200 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">My Organizations</p>
                                    <p class="text-sm text-gray-500">Manage your memberships</p>
                                </div>
                            </a>

                            <a href="<?php echo e(route('groups.my')); ?>" class="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                                <div class="w-10 h-10 bg-purple-100 group-hover:bg-purple-200 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">My Groups</p>
                                    <p class="text-sm text-gray-500">View your groups</p>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Account Settings</h3>
                        </div>

                        <div class="space-y-3">
                            <a href="<?php echo e(route('password.request')); ?>" class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Change Password</span>
                                </div>
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>

                            <button class="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 7l2.586 2.586A2 2 0 008.828 10h2.172a2 2 0 001.414-.586L15 7m0 0l4-4M7 7l4-4" />
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Privacy Settings</span>
                                </div>
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>

                            <button class="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 7l2.586 2.586A2 2 0 008.828 10h2.172a2 2 0 001.414-.586L15 7m0 0l4-4M7 7l4-4" />
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Notification Settings</span>
                                </div>
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Include Personal Post Creation Modal if it's the user's own profile -->
    <?php if(auth()->id() === $user->id): ?>
        <?php echo $__env->make('components.personal-post-creation-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    <style>
        /* Profile page specific styles */
        .profile-stats {
            background: linear-gradient(135deg, #7BC74D 0%, #393E46 100%);
        }

        .profile-cover {
            background: linear-gradient(135deg, #7BC74D 0%, #393E46 100%);
            position: relative;
            overflow: hidden;
        }

        .profile-cover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .profile-avatar-border {
            box-shadow: 0 0 0 4px white, 0 0 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .profile-avatar-border:hover {
            box-shadow: 0 0 0 4px white, 0 0 30px rgba(123, 199, 77, 0.3);
        }

        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Prevent content overflow */
        .profile-content {
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Responsive text truncation */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Smooth transitions for interactive elements */
        .transition-all {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Custom scrollbar for activity section */
        .activity-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .activity-scroll::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .activity-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .activity-scroll::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Mobile responsive improvements */
        @media (max-width: 640px) {
            .profile-cover {
                height: 12rem;
            }

            .profile-avatar-border {
                width: 5rem;
                height: 5rem;
            }
        }

        /* Tab navigation styles */
        .tab-navigation {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
        }

        /* Smooth tab transitions */
        [x-cloak] { display: none !important; }

        /* Tab content animations */
        .tab-content {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Sticky tab navigation */
        .sticky-tabs {
            position: sticky;
            top: 1rem;
            z-index: 10;
        }
    </style>

    <script>
        // Create Post Modal Alias Function
        function openCreatePostModal() {
            if (typeof openPersonalPostModal === 'function') {
                openPersonalPostModal();
            } else {
                console.error('Personal post modal function not available');
            }
        }

        // Background photo upload functionality
        function uploadBackgroundPhoto() {
            const form = document.getElementById('backgroundPhotoForm');
            const fileInput = document.getElementById('backgroundPhotoInput');

            if (fileInput.files.length > 0) {
                // Show loading state
                const uploadButton = document.querySelector('button[onclick="document.getElementById(\'backgroundPhotoInput\').click()"]');
                const originalText = uploadButton.innerHTML;
                uploadButton.innerHTML = '<svg class="w-4 h-4 inline mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span class="hidden sm:inline">Uploading...</span>';
                uploadButton.disabled = true;

                // Submit the form
                form.submit();
            }
        }

        // Background photo delete functionality
        function deleteBackgroundPhoto() {
            if (confirm('Are you sure you want to remove your background photo?')) {
                // Create and submit delete form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '<?php echo e(route("profile.background-photo.delete")); ?>';

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '<?php echo e(csrf_token()); ?>';

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);

                // Show loading state
                const deleteButton = document.querySelector('button[onclick="deleteBackgroundPhoto()"]');
                const originalText = deleteButton.innerHTML;
                deleteButton.innerHTML = '<svg class="w-4 h-4 inline mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span class="hidden sm:inline">Removing...</span>';
                deleteButton.disabled = true;

                form.submit();
            }
        }

        // File input validation (only if element exists - i.e., viewing own profile)
        const backgroundPhotoInput = document.getElementById('backgroundPhotoInput');
        if (backgroundPhotoInput) {
            backgroundPhotoInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Check file size (5MB limit)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size must be less than 5MB');
                        e.target.value = '';
                        return;
                    }

                    // Check file type
                    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPEG, PNG, JPG, or GIF)');
                        e.target.value = '';
                        return;
                    }
                }
            });
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/profile/show.blade.php ENDPATH**/ ?>